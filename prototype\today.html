<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>今日运势</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }
    .status-bar {
      background-color: #f8f8f8;
      height: 44px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      font-size: 12px;
      color: #000;
      position: sticky;
      top: 0;
      z-index: 10;
    }
    .tab-bar {
      background-color: #f8f8f8;
      height: 50px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      border-top: 1px solid #e5e5e5;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
    }
    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 10px;
    }
    .fortune-card {
      background: linear-gradient(135deg, #9333ea 0%, #7e22ce 100%);
      border-radius: 16px;
      padding: 20px;
      color: white;
      position: relative;
      overflow: hidden;
    }
    .fortune-mask {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 60%;
      background: linear-gradient(to top, rgba(255,255,255,0.95) 30%, transparent 100%);
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      align-items: center;
      padding-bottom: 20px;
    }
    .pay-button {
      background-color: #f59e0b;
      padding: 8px 20px;
      border-radius: 30px;
      font-weight: bold;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      margin-bottom: 10px;
      animation: bounce 1s infinite;
    }
    @keyframes bounce {
      0%, 100% {
        transform: translateY(0);
      }
      50% {
        transform: translateY(-6px);
      }
    }
    .star-filled {
      color: #fbbf24;
    }
    .star-empty {
      color: #e5e7eb;
    }
    .month-overview {
      background: linear-gradient(135deg, #f59e0b 0%, #ea580c 100%);
      border-radius: 16px;
      padding: 16px;
      color: white;
    }




.status-bar {
            background-color: #fff;
            height: 44px;
            padding: 0 16px;
            display: flex;
            align-items: center;
            position: relative;
            box-shadow: 0 1px 1px rgba(0,0,0,0.05);
        }
        .status-bar-title {
            font-size: 16px;
            font-weight: 500;
            margin: 0 auto;
        }
        .payment-card {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 16px;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .feature-icon {
            width: 24px;
            height: 24px;
            background-color: #f0e6ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #8A2BE2;
            flex-shrink: 0;
        }
        .payment-button {
            background: linear-gradient(90deg, #8A2BE2, #4B0082);
            color: white;
            font-weight: bold;
            width: 100%;
            padding: 12px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(138, 43, 226, 0.3);
        }
        .payment-method {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #eee;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
        }
        .payment-method.active {
            border-color: #8A2BE2;
            background-color: #f0e6ff;
        }
        .method-icon {
            width: 32px;
            height: 32px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        .zodiac-preview {
            background: linear-gradient(135deg, #FF8C00, #FF4500);
            color: white;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            overflow: hidden;
            margin-bottom: 16px;
         

        }
        .zodiac-preview::before {
            content: '';
            position: absolute;
            bottom: -30px;
            left: -30px;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            z-index: 1;
        }
        .day-preview {
            display: flex;
            margin-top: 12px;
            overflow-x: auto;
            gap: 8px;
            padding-bottom: 8px;
        }
        .day-item {
            min-width: 60px;
            background-color: rgba(255,255,255,0.2);
            padding: 8px;
            border-radius: 8px;
            text-align: center;
            font-size: 12px;
        }
        .day-number {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        .blur-overlay {
            filter: blur(3px);
        }
        .lock-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .coupon-item {
            border: 1px dashed #8A2BE2;
            border-radius: 8px;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
            background-color: #f9f5ff;
        }
        .coupon-amount {
            color: #8A2BE2;
            font-weight: bold;
            font-size: 16px;
        }
        .coupon-desc {
            font-size: 12px;
            color: #888;
        }



  </style>
</head>
<body class="bg-gray-100">
  <!-- 状态栏 -->
  <div class="status-bar">
    <div class="flex items-center">
      <span>4G</span>
      <span class="ml-1">5G</span>
      <span class="ml-1">HD</span>
      <span class="ml-2">16:58</span>
    </div>
    <div class="flex items-center">
      <i class="fas fa-wifi mr-2"></i>
      <span class="bg-gray-300 text-white rounded-full px-2 text-xs">68</span>
    </div>
  </div>






  <!-- 主内容 -->
  <div class="p-4 pb-20">
    <!-- 今日运势标题 -->
    <h2 class="text-xl font-medium text-gray-700 mb-4 mt-20">今日运势</h2>
            <div class="zodiac-preview">
            <!-- 今日运势标题 -->
 
             <div class="flex items-center mb-3">
                <i class="fas fa-mouse text-white text-xl mr-2"></i>
                  <span class="font-bold mr-1">鼠</span>
                 <i class="fas fa-mars text-blue-200 ml-1"></i>
               <div class="ml-auto bg-white text-purple-700 rounded-full px-3 py-1 text-sm font-medium">5月12日</div>
          </div>
            
            <div class="mt-4 relative">
                <div class="blur-overlay">
                    <div class="text-base font-medium">本月概要</div>
                    <p class="mt-1 text-sm opacity-85">5月整体运势呈上升趋势，中旬会有贵人相助，下旬要注意健康问题。工作上新机遇与挑战并存...</p>
                    
                    <div class="day-preview">
                        <div class="day-item">
                            <div class="day-number">13</div>
                            <div><i class="fas fa-arrow-up text-green-300"></i></div>
                        </div>
                        <div class="day-item">
                            <div class="day-number">14</div>
                            <div><i class="fas fa-arrow-up text-green-300"></i></div>
                        </div>
                        <div class="day-item">
                            <div class="day-number">15</div>
                            <div><i class="fas fa-arrow-down text-red-300"></i></div>
                        </div>
                        <div class="day-item">
                            <div class="day-number">16</div>
                            <div><i class="fas fa-minus text-yellow-300"></i></div>
                        </div>
                        <div class="day-item">
                            <div class="day-number">17</div>
                            <div><i class="fas fa-arrow-up text-green-300"></i></div>
                        </div>
                    </div>
                </div>
                
                <div class="lock-overlay">
                    <i class="fas fa-lock text-4xl text-white"></i>
                </div>
            </div>
        </div>


     
















  <!-- 主内容 -->
  <div class="p-4 pb-20">
    <!-- 今日运势标题 -->
               <div class="p-4 pb-20">
    
                  <button class="pay-button w-full">付费￥19.9元解锁全部运势</button>
                  <p class="text-gray-700 text-sm text-center">付费后可查看明日运势和本月运势</p>

                 </div>
    
    <!-- 本月运势概览 -->
    <h2 class="text-xl font-medium text-gray-700 mb-4">本月运势概览</h2>
    <div class="month-overview">
      <div class="flex items-center mb-3">
        <span class="font-bold text-lg">5月运势</span>
        <button class="ml-auto bg-white text-purple-700 rounded-full px-3 py-1 text-sm font-medium">
          查看详情
        </button>
      </div>
      <p class="text-sm">本月概要</p>
    </div>
  </div>

  <!-- 底部标签栏 -->
  <div class="tab-bar">
    <div class="tab-item">
      <i class="fas fa-list text-gray-400"></i>
      <span class="text-gray-400">菜单</span>
    </div>
    <div class="tab-item">
      <i class="fas fa-home text-gray-400"></i>
      <span class="text-gray-400">首页</span>
    </div>
    <div class="tab-item">
      <i class="fas fa-arrow-left text-gray-400"></i>
      <span class="text-gray-400">返回</span>
    </div>
  </div>
</body>
</html> 