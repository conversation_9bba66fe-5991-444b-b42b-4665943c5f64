<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>选择生肖</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }
    .status-bar {
      background-color: #f8f8f8;
      height: 44px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      font-size: 12px;
      color: #000;
      position: sticky;
      top: 0;
      z-index: 10;
    }
    .tab-bar {
      background-color: #f8f8f8;
      height: 50px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-around;
      border-top: 1px solid #e5e5e5;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
    }
    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 10px;
    }
    .selected-gender {
      background-color: #9333ea;
      color: white;
    }
    .unselected-gender {
      background-color: #f3f4f6;
      color: #6b7280;
    }
    .zodiac-item {
      width: 70px;
      height: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .zodiac-icon {
      width: 55px;
      height: 55px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 5px;
    }
    .selected-zodiac {
      background-color: #f3e8ff;
    }
    .selected-zodiac .zodiac-icon {
      background-color: #9333ea;
      color: white;
    }
    .selected-zodiac .zodiac-name {
      color: #9333ea;
    }
    .unselected-zodiac .zodiac-icon {
      background-color: #f3f4f6;
      color: #9ca3af;
    }
  </style>
</head>
<body class="bg-gray-100">
  <!-- 状态栏 -->
  <div class="status-bar">
    <div class="flex items-center">
      <span>4G</span>
      <span class="ml-1">5G</span>
      <span class="ml-1">HD</span>
      <span class="ml-2">16:58</span>
    </div>
    <div class="flex items-center">
      <i class="fas fa-wifi mr-2"></i>
      <span class="bg-gray-300 text-white rounded-full px-2 text-xs">68</span>
    </div>
  </div>

  <!-- 主内容 -->
  <div class="p-4 pb-20">
    <!-- 性别选择 -->
    <div class="flex w-full rounded-full overflow-hidden mb-8">
      <button class="flex-1 selected-gender py-3 flex items-center justify-center">
        <i class="fas fa-mars mr-2"></i> 男
      </button>
      <button class="flex-1 unselected-gender py-3 flex items-center justify-center">
        <i class="fas fa-venus mr-2"></i> 女
      </button>
    </div>

    <!-- 生肖选择文字提示 -->
    <h2 class="text-xl font-medium text-gray-700 mb-6">请选择您的生肖</h2>

    <!-- 生肖选择 -->
    <div class="grid grid-cols-4 gap-2 mb-4">
      <!-- 鼠 - 选中状态 -->
      <div class="zodiac-item selected-zodiac">
        <div class="zodiac-icon">
          <i class="fas fa-mouse text-2xl"></i>
        </div>
        <span class="zodiac-name font-medium">鼠</span>
      </div>
      
      <!-- 牛 - 未选中状态 -->
      <div class="zodiac-item unselected-zodiac">
        <div class="zodiac-icon">
          <i class="fas fa-cow text-2xl"></i>
        </div>
        <span class="text-gray-400">牛</span>
      </div>
      
      <!-- 虎 - 未选中状态 -->
      <div class="zodiac-item unselected-zodiac">
        <div class="zodiac-icon">
          <i class="fas fa-dragon text-2xl"></i>
        </div>
        <span class="text-gray-400">虎</span>
      </div>
      
      <!-- 兔 - 未选中状态 -->
      <div class="zodiac-item unselected-zodiac">
        <div class="zodiac-icon">
          <i class="fas fa-dragon text-2xl"></i>
        </div>
        <span class="text-gray-400">兔</span>
      </div>
    </div>

    <!-- 第二行生肖 -->
    <div class="grid grid-cols-4 gap-2 mb-4">
      <div class="zodiac-item unselected-zodiac">
        <div class="zodiac-icon">
          <i class="fas fa-dragon text-2xl"></i>
        </div>
        <span class="text-gray-400">龙</span>
      </div>
      <div class="zodiac-item unselected-zodiac">
        <div class="zodiac-icon">
          <i class="fas fa-kiwi-bird text-2xl"></i>
        </div>
        <span class="text-gray-400">蛇</span>
      </div>
      <div class="zodiac-item unselected-zodiac">
        <div class="zodiac-icon">
          <i class="fas fa-horse text-2xl"></i>
        </div>
        <span class="text-gray-400">马</span>
      </div>
      <div class="zodiac-item unselected-zodiac">
        <div class="zodiac-icon">
          <i class="fas fa-sheep text-2xl"></i>
        </div>
        <span class="text-gray-400">羊</span>
      </div>
    </div>

    <!-- 第三行生肖 -->
    <div class="grid grid-cols-4 gap-2 mb-8">
      <div class="zodiac-item unselected-zodiac">
        <div class="zodiac-icon">
          <i class="fas fa-monkey text-2xl"></i>
        </div>
        <span class="text-gray-400">猴</span>
      </div>
      <div class="zodiac-item unselected-zodiac">
        <div class="zodiac-icon">
          <i class="fas fa-kiwi-bird text-2xl"></i>
        </div>
        <span class="text-gray-400">鸡</span>
      </div>
      <div class="zodiac-item unselected-zodiac">
        <div class="zodiac-icon">
          <i class="fas fa-dog text-2xl"></i>
        </div>
        <span class="text-gray-400">狗</span>
      </div>
      <div class="zodiac-item unselected-zodiac">
        <div class="zodiac-icon">
          <i class="fas fa-piggy-bank text-2xl"></i>
        </div>
        <span class="text-gray-400">猪</span>
      </div>
    </div>

    <!-- 选择指示条 -->
    <div class="w-1/4 h-1 bg-gray-300 mx-auto mb-8"></div>
  </div>

  <!-- 底部标签栏 -->
  <div class="tab-bar">
    <div class="tab-item">
      <i class="fas fa-list text-gray-400"></i>
      <span class="text-gray-400">菜单</span>
    </div>
    <div class="tab-item">
      <i class="fas fa-home text-gray-400"></i>
      <span class="text-gray-400">首页</span>
    </div>
    <div class="tab-item">
      <i class="fas fa-arrow-left text-gray-400"></i>
      <span class="text-gray-400">返回</span>
    </div>
  </div>
</body>
</html> 